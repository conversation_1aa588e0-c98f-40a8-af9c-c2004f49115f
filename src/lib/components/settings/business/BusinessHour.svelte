<script lang="ts">
	import { t } from '$src/lib/stores/i18n.ts';
	import { enhance } from '$app/forms';
	import { Button, Alert } from 'flowbite-svelte';
	import { invalidateAll } from '$app/navigation';
	import { CheckOutline } from 'flowbite-svelte-icons';

	// props passed in from the page load
	export let businessHourSettings: any;

	// Initialize days and toggle state
	let days = [];
	let sameAsBusinessHours = true;

	// Simple boolean flag for change detection
	let formHasUnsavedChanges = false;

	// Time validation state
	let timeValidationErrors = {};
	let hasValidationErrors = false;

	// Error handling state
	let showErrorMessage = false;
	let errorMessage = '';
	let fieldErrors = {};

	// Function to parse error messages (supports both single string and multi-field errors)
	function parseErrorMessages(error) {
		if (!error) return { fieldErrors: {}, generalError: null };

		let errorObj = error;

		// Handle string errors - check if it's a JSON string first
		if (typeof error === 'string') {
			// Try to parse as JSON first
			try {
				errorObj = JSON.parse(error);
			} catch (e) {
				// If JSON parsing fails, treat as plain string (backward compatibility)
				return { fieldErrors: {}, generalError: error };
			}
		}

		// Handle object errors with field-specific messages
		if (typeof errorObj === 'object' && errorObj !== null) {
			const fieldErrors = {};
			let hasFieldErrors = false;

			for (const [fieldName, fieldErrorArray] of Object.entries(errorObj)) {
				if (Array.isArray(fieldErrorArray)) {
					const validErrors = fieldErrorArray.filter((msg) => typeof msg === 'string');
					if (validErrors.length > 0) {
						fieldErrors[fieldName] = validErrors;
						hasFieldErrors = true;
					}
				}
			}

			if (hasFieldErrors) {
				return { fieldErrors, generalError: null };
			}
		}

		return { fieldErrors: {}, generalError: 'An error occurred' };
	}

	// Function to dismiss alerts when input changes
	function dismissAlerts() {
		showErrorMessage = false;
		errorMessage = '';
		fieldErrors = {};
	}

	// Reactive statement to initialize/update form when businessHourSettings changes
	$: if (businessHourSettings?.workShift) {
		days = businessHourSettings.workShift || [];
		sameAsBusinessHours = true;
		// Reset the unsaved changes flag when form is initialized with server data
		formHasUnsavedChanges = false;
		// Validate all days after loading
		setTimeout(() => validateAllDays(), 0);
	}

	// When sending, ensure inactive days have empty times
	$: cleanedDays = days.map((d) => ({
		day: d.day,
		active: d.active,
		times: d.active ? d.times : []
	}));

	// Function to mark form as having unsaved changes
	function markFormAsChanged() {
		formHasUnsavedChanges = true;
		dismissAlerts(); // Dismiss alerts when form changes
	}

	// Local implementation of handleEnhance without the page data update
	function localHandleEnhance(options) {
		return async ({ result, update }) => {
			// Set pending state if the option is provided
			options.setPending?.(true);

			if (result.type === 'failure') {
				options.setErrorMessage?.(result.data?.error || t('business_hours_update_error'));
				// Don't close modal on error
			} else if (result.type === 'success') {
				const successMessage = result.data.res_msg || t('business_hours_update_success');

				if (options.useToastOnSuccess) {
					// Import toastStore locally to avoid module-level import issues
					const { toastStore } = await import('$lib/stores/toastStore');
					toastStore.add(successMessage, 'success');
				} else {
					// Existing behavior for backward compatibility
					options.setShowSuccessMessage?.(true);
					options.setSuccessMessage?.(successMessage);
				}

				if (options.closeModalOnSuccess) {
					options.setModalOpen?.(false);
				}
			}
			// NOTE: Removed the "await update();" call to prevent page data refresh
			// that interferes with form state management

			// Invalidate all to refresh the page data
			invalidateAll();

			// Reset pending state if the option is provided
			options.setPending?.(false);
		};
	}

	// Custom enhance function for post-submission state management
	function customEnhance() {
		return async ({ result, update }) => {
			// Handle the standard enhancement (error handling, toast notifications)
			// using local implementation without page data update
			const localStandardEnhance = localHandleEnhance(enhanceOptions);
			await localStandardEnhance({ result, update });

			// After successful submission, disable Save button but keep all form values
			if (result.type === 'success') {
				formHasUnsavedChanges = false; // Disable Save button immediately
			}
		};
	}

	// Options for handling form enhancement
	$: enhanceOptions = {
		modalOpen: false, // Not a modal component
		setModalOpen: () => {}, // No modal to close
		setShowSuccessMessage: () => {}, // Not used with toast
		setSuccessMessage: () => {}, // Not used with toast
		setShowErrorMessage: (value) => (showErrorMessage = value),
		setErrorMessage: (value) => {
			const parsedResult = parseErrorMessages(value);
			fieldErrors = parsedResult.fieldErrors;
			errorMessage = parsedResult.generalError || '';
			// Set showErrorMessage to true when we have either field errors or general error
			showErrorMessage =
				Object.keys(parsedResult.fieldErrors).length > 0 || parsedResult.generalError !== null;
		},
		// Enhanced success behavior - use toast notifications
		useToastOnSuccess: true,
		closeModalOnSuccess: false // Not a modal component
	};

	// Generate time options every 30 minutes
	const timeOptions = Array.from({ length: 48 }, (_, i) => {
		const hour = Math.floor(i / 2)
			.toString()
			.padStart(2, '0');
		const minutes = i % 2 === 0 ? '00' : '30';
		return `${hour}:${minutes}`;
	});

	// Function to convert time string (HH:MM) to minutes since midnight for comparison
	function timeToMinutes(timeStr: string): number {
		if (!timeStr) return 0;
		const [hours, minutes] = timeStr.split(':').map(Number);
		return hours * 60 + minutes;
	}

	// Function to validate time range for a specific day
	function validateDayTimes(dayIndex: number): boolean {
		const day = days[dayIndex];
		if (!day || !day.active || !day.times || !day.times[0]) {
			// Clear any existing errors for this day
			delete timeValidationErrors[dayIndex];
			timeValidationErrors = { ...timeValidationErrors };
			return true;
		}

		const startTime = day.times[0].start;
		const endTime = day.times[0].end;

		if (!startTime || !endTime) {
			delete timeValidationErrors[dayIndex];
			timeValidationErrors = { ...timeValidationErrors };
			return true;
		}

		const startMinutes = timeToMinutes(startTime);
		const endMinutes = timeToMinutes(endTime);

		if (startMinutes >= endMinutes) {
			timeValidationErrors[dayIndex] = t('tab_schedule_error_start_time');
			timeValidationErrors = { ...timeValidationErrors };
			return false;
		} else {
			delete timeValidationErrors[dayIndex];
			timeValidationErrors = { ...timeValidationErrors };
			return true;
		}
	}

	// Reactive statement to check if there are any validation errors
	$: hasValidationErrors = Object.keys(timeValidationErrors).length > 0;

	// Function to validate all days
	function validateAllDays(): boolean {
		let allValid = true;
		for (let i = 0; i < days.length; i++) {
			if (!validateDayTimes(i)) {
				allValid = false;
			}
		}
		return allValid;
	}

	// Function to map day names to translation keys
	function getDayTranslationKey(dayName: string): string {
		if (!dayName) return 'day_monday'; // fallback

		const normalizedDay = dayName.toLowerCase().trim();

		// Handle various day name formats
		const dayMapping: Record<string, string> = {
			monday: 'day_monday',
			mon: 'day_monday',
			tuesday: 'day_tuesday',
			tue: 'day_tuesday',
			wednesday: 'day_wednesday',
			wed: 'day_wednesday',
			thursday: 'day_thursday',
			thu: 'day_thursday',
			friday: 'day_friday',
			fri: 'day_friday',
			saturday: 'day_saturday',
			sat: 'day_saturday',
			sunday: 'day_sunday',
			sun: 'day_sunday',
			// Handle Thai day names in case server sends them
			วันจันทร์: 'day_monday',
			วันอังคาร: 'day_tuesday',
			วันพุธ: 'day_wednesday',
			วันพฤหัสบดี: 'day_thursday',
			วันศุกร์: 'day_friday',
			วันเสาร์: 'day_saturday',
			วันอาทิตย์: 'day_sunday'
		};

		return dayMapping[normalizedDay] || 'day_monday'; // fallback to Monday
	}

	// Toggle a day on/off, clearing or defaulting its times
	function toggleDay(index: number) {
		days[index].active = !days[index].active;
		if (!days[index].active) {
			days[index].times = [];
		} else if (days[index].times.length === 0) {
			// default to 09:00-18:00 when re-activated
			days[index].times = [{ start: '09:00', end: '18:00' }];
		}
		days = [...days]; // trigger reactivity
		markFormAsChanged(); // Mark form as changed and dismiss alerts
		// Validate the day after toggling
		setTimeout(() => validateDayTimes(index), 0);
	}

	// Handle time change with validation
	function handleTimeChange(dayIndex: number, timeType: string) {
		days = [...days]; // force reactivity
		markFormAsChanged(); // Mark form as changed
		setTimeout(() => validateDayTimes(dayIndex), 0);
	}

	// Handle form submission with validation
	function handleSubmit(event: Event) {
		if (!validateAllDays()) {
			event.preventDefault();
			return false;
		}
		return true;
	}
</script>

<form
	id="settings-business-business-hour-form"
	method="POST"
	action="?/update_user_work_schedule"
	class="space-y-4 rounded-lg bg-white p-6 shadow-md"
	on:submit={handleSubmit}
	use:enhance={customEnhance}
>
	<!-- Error display -->
	{#if showErrorMessage}
		<Alert id="settings-business-business-hour-error" color="red" class="mb-4">
			{errorMessage}
		</Alert>
	{/if}
	<!-- Field-specific error display for multi-field errors -->
	{#if Object.keys(fieldErrors).length > 0}
		{#each Object.entries(fieldErrors) as [fieldName, errors], errorIndex}
			{#each errors as error, subErrorIndex}
				<Alert id="settings-business-business-hour-field-error-{errorIndex}-{subErrorIndex}" color="red" class="mb-4">
					<strong>{fieldName}:</strong>
					{error}
				</Alert>
			{/each}
		{/each}
	{/if}

	<!-- Hidden inputs for server action -->
	<input id="settings-business-business-hour-same-as-business" type="hidden" name="sameAsBusinessHours" value={sameAsBusinessHours} />
	<input id="settings-business-business-hour-work-shift-data" type="hidden" name="workShiftData" value={JSON.stringify(cleanedDays)} />

	<div class="flex w-full items-center justify-between">
		<div>
			<h2 id="settings-business-business-hour-title" class="text-xl font-medium text-gray-700">{t('business_hours')}</h2>
			<p id="settings-business-business-hour-description" class="text-sm text-gray-600">{t('business_hours_description')}</p>
		</div>
		<Button
			id="settings-business-business-hour-save"
			type="submit"
			disabled={hasValidationErrors || !formHasUnsavedChanges}
			color="green"
			class="inline-flex items-center rounded-md border border-transparent text-sm font-medium shadow-sm disabled:cursor-not-allowed disabled:opacity-20"
		>
			<CheckOutline class="mr-2 h-4 w-4" />{t('save')}
		</Button>
	</div>

	<div class="space-y-4">
		{#if days.length > 0}
			{#each days as day, i}
				<div id="settings-business-business-hour-day-{i}" class="mb-4">
					<!-- Mobile layout: Day checkbox and label stacked above time controls -->
					<div class="flex flex-col space-y-2 md:hidden">
						<div class="flex items-center">
							<div class="w-10 flex items-center">
								<input
									type="checkbox"
									id="settings-business-business-hour-day-mobile-checkbox-{i}"
									checked={day.active}
									on:change={() => toggleDay(i)}
									class="h-5 w-5 rounded border-gray-300 bg-gray-100 text-blue-500 focus:ring-2 focus:ring-blue-500"
								/>
							</div>
							<label for="settings-business-business-hour-day-mobile-checkbox-{i}" class="w-32 font-medium text-gray-700">
								{t(getDayTranslationKey(day.day))}
							</label>
						</div>

						{#if day.active && day.times && day.times.length > 0}
							<!-- Time selection - mobile stacked layout -->
							<div class="ml-10 space-y-2">
								<div class="relative inline-block w-full">
									<select
										id="settings-business-business-hour-day-mobile-start-{i}"
										bind:value={day.times[0].start}
										on:change={() => handleTimeChange(i, 'start')}
										class="block w-full appearance-none rounded border px-4 py-2 pr-8 leading-tight text-gray-700 focus:outline-none {timeValidationErrors[
											i
										]
											? 'border-red-500 focus:border-red-500'
											: 'border-gray-300 focus:border-blue-500'} bg-white"
									>
										{#each timeOptions as option}
											<option value={option}>{option}</option>
										{/each}
									</select>
								</div>

								<span class="flex text-center text-gray-600">{t('to')}</span>

								<div class="relative inline-block w-full">
									<select
										id="settings-business-business-hour-day-mobile-end-{i}"
										bind:value={day.times[0].end}
										on:change={() => handleTimeChange(i, 'end')}
										class="block w-full appearance-none rounded border px-4 py-2 pr-8 leading-tight text-gray-700 focus:outline-none {timeValidationErrors[
											i
										]
											? 'border-red-500 focus:border-red-500'
											: 'border-gray-300 focus:border-blue-500'} bg-white"
									>
										{#each timeOptions as option}
											<option value={option}>{option}</option>
										{/each}
									</select>
								</div>
							</div>

							<!-- Time validation error for this specific day - mobile -->
							{#if timeValidationErrors[i]}
								<div class="ml-10 mt-2">
									<Alert id="settings-business-business-hour-day-mobile-error-{i}" color="red" class="px-3 py-2 text-sm">
										{timeValidationErrors[i]}
									</Alert>
								</div>
							{/if}
						{:else if day.active}
							<div class="ml-10 flex flex-1 items-center">
								<span id="settings-business-business-hour-day-mobile-no-times-{i}" class="italic text-gray-500">No times set</span>
							</div>
						{/if}
					</div>

					<!-- Desktop layout: Day checkbox and time controls on same line -->
					<div class="hidden md:flex md:items-center md:space-x-4">
						<div class="flex items-center">
							<div class="w-10">
								<input
									type="checkbox"
									id="settings-business-business-hour-day-desktop-checkbox-{i}"
									checked={day.active}
									on:change={() => toggleDay(i)}
									class="h-5 w-5 rounded border-gray-300 bg-gray-100 text-blue-500 focus:ring-2 focus:ring-blue-500"
								/>
							</div>
							<label for="settings-business-business-hour-day-desktop-checkbox-{i}" class="w-32 font-medium text-gray-700">
								{t(getDayTranslationKey(day.day))}
							</label>
						</div>

						{#if day.active && day.times && day.times.length > 0}
							<!-- Time selection - desktop inline layout -->
							<div class="flex items-center space-x-3">
								<div class="relative inline-block w-40">
									<select
										id="settings-business-business-hour-day-desktop-start-{i}"
										bind:value={day.times[0].start}
										on:change={() => handleTimeChange(i, 'start')}
										class="block w-full appearance-none rounded border px-4 py-2 pr-8 leading-tight text-gray-700 focus:outline-none {timeValidationErrors[
											i
										]
											? 'border-red-500 focus:border-red-500'
											: 'border-gray-300 focus:border-blue-500'} bg-white"
									>
										{#each timeOptions as option}
											<option value={option}>{option}</option>
										{/each}
									</select>
								</div>

								<span class="text-gray-600">{t('to')}</span>

								<div class="relative inline-block w-40">
									<select
										id="settings-business-business-hour-day-desktop-end-{i}"
										bind:value={day.times[0].end}
										on:change={() => handleTimeChange(i, 'end')}
										class="block w-full appearance-none rounded border px-4 py-2 pr-8 leading-tight text-gray-700 focus:outline-none {timeValidationErrors[
											i
										]
											? 'border-red-500 focus:border-red-500'
											: 'border-gray-300 focus:border-blue-500'} bg-white"
									>
										{#each timeOptions as option}
											<option value={option}>{option}</option>
										{/each}
									</select>
								</div>
							</div>
						{:else if day.active}
							<div class="flex flex-1 items-center">
								<span id="settings-business-business-hour-day-desktop-no-times-{i}" class="italic text-gray-500">No times set</span>
							</div>
						{/if}
					</div>

					<!-- Time validation error for this specific day - desktop -->
					{#if timeValidationErrors[i]}
						<div class="hidden md:ml-44 md:mt-2 md:block">
							<Alert id="settings-business-business-hour-day-desktop-error-{i}" color="red" class="px-3 py-2 text-sm">
								{timeValidationErrors[i]}
							</Alert>
						</div>
					{/if}
				</div>
			{/each}
		{:else}
			<p id="settings-business-business-hour-loading" class="text-gray-500">Loading business hours...</p>
		{/if}
	</div>
</form>
