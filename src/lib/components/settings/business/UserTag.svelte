<!-- UserTag.svelte -->
<script lang="ts">
	import { t } from '$lib/stores/i18n';

	import {
		PlusOutline,
		MinusOutline,
		TrashBinSolid,
		EditOutline,
		CheckOutline
	} from 'flowbite-svelte-icons';
	import { enhance } from '$app/forms';
	import { invalidateAll } from '$app/navigation';
	import { AccordionItem, Indicator, Button } from 'flowbite-svelte';
	import { onMount, tick } from 'svelte';

	interface Tag {
		id: number;
		name: string;
		color?: string;
	}
	interface ColorOption {
		name: string;
		class: string;
	}

	export let userTagNames: Tag[] = [];
	import { colorOptions, getColorClass } from '$lib/utils'; // adjust the path if needed

	// State for editing and adding tags
	let isAddingTag = false;
	let tagFormErrors: string | null = null;
	let tagToDelete: number | null = null;
	let tagToEdit: number | null = null;
	let isSubmitting = false;

	// Shared state
	let editName = '';
	let selectedColor = colorOptions[0]?.name || 'gray';
	let colorPickerOpen = false;
	let activePickerId: string | null = null;

	// State specifically for new tag
	let newTagName = '';
	let newTagColor = colorOptions[0]?.name || 'gray';

	onMount(() => {
		const handleClickOutside = (event: MouseEvent) => {
			if (colorPickerOpen && !(event.target as HTMLElement).closest('.color-picker-area')) {
				colorPickerOpen = false;
				activePickerId = null;
			}
		};
		document.addEventListener('click', handleClickOutside);
		return () => document.removeEventListener('click', handleClickOutside);
	});

	async function toggleColorPicker(id: string) {
		if (activePickerId === id && colorPickerOpen) {
			colorPickerOpen = false;
			activePickerId = null;
		} else {
			activePickerId = id;
			await tick();
			colorPickerOpen = true;
		}
	}

	function chooseColor(name: string) {
		if (tagToEdit !== null) {
			selectedColor = name;
		} else {
			newTagColor = name;
		}
		colorPickerOpen = false;
		activePickerId = null;
	}

	function handleSubmit() {
		isSubmitting = true;
		tagFormErrors = null;
		return async ({ result }: any) => {
			isSubmitting = false;
			if (result.type === 'success') {
				tagToEdit = null;
				newTagName = '';
				isAddingTag = false; // ← close the add-form on success
				await invalidateAll();
			} else {
				tagFormErrors = result.data?.error || 'Error';
			}
		};
	}

	function startEdit(tag: Tag) {
		tagToEdit = tag.id;
		editName = tag.name;
		selectedColor = tag.color || selectedColor;
		activePickerId = null;
		tagFormErrors = null;
	}

	function cancelEdit() {
		tagToEdit = null;
		tagFormErrors = null;
		colorPickerOpen = false;
		activePickerId = null;
	}

	function confirmDel(id: number) {
		tagToDelete = id;
	}
	function cancelDel() {
		tagToDelete = null;
	}
</script>

<AccordionItem>
	<span slot="header" class="flex w-full flex-col">
		<h2 class="text-xl font-medium text-gray-700">{t('specialized_tags')}</h2>
		<p class="text-sm text-gray-500">{t('tag_members_by_role')}</p>
	</span>

	<!-- Existing Tags List -->
	<div class="space-y-3">
		{#each userTagNames as tag (tag.id)}
			{#if tagToEdit === tag.id}
				<!-- Edit Mode -->
				<li id="settings-business-user-tag-item-{tag.id}" class="flex items-center justify-between rounded-lg px-4 py-2">
					<div class="relative flex flex-1 items-center gap-3">
						<button
							id="settings-business-user-tag-edit-color-picker-{tag.id}"
							type="button"
							class="flex items-center"
							on:click|stopPropagation={() => toggleColorPicker(`tag-${tag.id}`)}
							aria-label="Select tag color"
						>
							<!-- <Indicator size="lg" color={selectedColor} class="mr-2" /> -->
							<Indicator size="lg" class={`mr-1 ${getColorClass(selectedColor)}`} />
						</button>
						{#if activePickerId === `tag-${tag.id}` && colorPickerOpen}
							<div
								id="settings-business-user-tag-edit-color-dropdown-{tag.id}"
								class="color-picker-area absolute bottom-full left-0 z-20 mb-2 rounded-lg bg-white p-3 shadow-lg"
								style="min-width: 170px;"
							>
								<div class="grid grid-cols-6 gap-3">
									{#each colorOptions as opt}
										<button
											id="settings-business-user-tag-edit-color-option-{tag.id}-{opt.name}"
											type="button"
											class={`h-6 w-6 cursor-pointer rounded-full ${opt.class} border ${selectedColor === opt.name ? 'ring-2 ring-gray-400' : 'border-transparent'}`}
											on:click|stopPropagation={() => chooseColor(opt.name)}
											aria-label={`Select ${opt.name} color`}
										></button>
									{/each}
								</div>
							</div>
						{/if}
						<form
							id="settings-business-user-tag-edit-form-{tag.id}"
							method="POST"
							action="?/update_tag"
							use:enhance={handleSubmit}
							class="flex flex-1 items-center gap-2"
						>
							<input type="hidden" name="tag_id" value={tag.id} />
							<input type="hidden" name="color" value={selectedColor} />
							<input
								type="text"
								id="settings-business-user-tag-edit-name-{tag.id}"
								name="name"
								bind:value={editName}
								class="flex-1 rounded-lg border border-gray-300 bg-white px-3 py-1 focus:ring-2 focus:ring-blue-400"
								placeholder={t('enter_tag_name')}
							/>

							<Button
								id="settings-business-user-tag-edit-save-{tag.id}"
								type="submit"
								disabled={isSubmitting}
								color="green"
							>
								<CheckOutline class="mr-2 h-4 w-4" />
								{isSubmitting ? t('updating') : t('save')}
							</Button>
							<Button
								id="settings-business-user-tag-edit-cancel-{tag.id}"
								type="button"
								on:click={cancelEdit}
								color="light"
							>
								{t('cancel')}
							</Button>
						</form>
					</div>
				</li>
			{:else}
				<!-- Display Mode -->
				<li id="settings-business-user-tag-display-{tag.id}" class="flex items-center justify-between rounded-lg px-4 py-2">
					<div class="flex items-center gap-3">
						<button type="button" class="flex items-center">
							<!-- <Indicator size="lg" color={tag.color ?? 'gray'} class="mr-2" /> -->
							<Indicator size="lg" class={`${getColorClass(tag.color || 'gray')} mr-2`} />
							<span id="settings-business-user-tag-name-{tag.id}" class="font-xl text-md text-gray-900">{tag.name}</span>
						</button>
					</div>

					<div class="flex items-center gap-2">
						{#if tagToDelete === tag.id}
							<form
								id="settings-business-user-tag-delete-form-{tag.id}"
								method="POST"
								action="?/delete_tag"
								use:enhance={handleSubmit}
								class="flex items-center gap-2"
							>
								<input type="hidden" name="tag_id" value={tag.id} />
								<button
									id="settings-business-user-tag-delete-confirm-{tag.id}"
									type="submit"
									class="text-sm text-red-600 hover:text-red-800 hover:underline"
								>
									{t('delete')}
								</button>
								<button
									id="settings-business-user-tag-delete-cancel-{tag.id}"
									type="button"
									on:click={cancelDel}
									class="text-sm text-gray-500 hover:text-gray-800 hover:underline"
								>
									{t('cancel')}
								</button>
							</form>
						{:else}
							<button
								id="settings-business-user-tag-edit-button-{tag.id}"
								on:click={() => startEdit(tag)}
								class="text-gray-400 hover:text-gray-800"
								aria-label="Edit tag"
							>
								<EditOutline class="h-5 w-5" />
							</button>
							<button
								id="settings-business-user-tag-delete-button-{tag.id}"
								on:click={() => confirmDel(tag.id)}
								class="text-red-500 hover:text-red-700"
								aria-label="Delete tag"
							>
								<TrashBinSolid class="h-5 w-5" />
							</button>
						{/if}
					</div>
				</li>
			{/if}

			<!-- Divider line between sections -->
			<hr class="my-6 border-t border-gray-300" />
		{/each}

		<!-- New Tag Form -->
		<div class="w-full">
			{#if isAddingTag}
				<div class="relative flex items-center gap-3 pr-4">
					<button
						id="settings-business-user-tag-new-color-picker"
						type="button"
						class="flex items-center pl-4"
						on:click|stopPropagation={() => toggleColorPicker('new-tag')}
						aria-label="Select tag color"
					>
						<!-- <Indicator size="lg" color={newTagColor} class="mr-2" /> -->
						<Indicator size="lg" class={`mr-1 ${getColorClass(newTagColor)}`} />
					</button>
					{#if activePickerId === 'new-tag' && colorPickerOpen}
						<div
							id="settings-business-user-tag-new-color-dropdown"
							class="color-picker-area absolute bottom-full left-0 z-20 mb-2 rounded-lg bg-white p-3 shadow-lg"
							style="min-width: 170px;"
						>
							<div class="grid grid-cols-6 gap-3">
								{#each colorOptions as opt}
									<button
										id="settings-business-user-tag-new-color-option-{opt.name}"
										type="button"
										class={`h-6 w-6 cursor-pointer rounded-full ${opt.class} border ${newTagColor === opt.name ? 'ring-2 ring-gray-400' : 'border-transparent'}`}
										on:click|stopPropagation={() => chooseColor(opt.name)}
										aria-label={`Select ${opt.name} color`}
									></button>
								{/each}
							</div>
						</div>
					{/if}

					<form
						id="settings-business-user-tag-new-form"
						method="POST"
						action="?/create_new_tag_action"
						use:enhance={handleSubmit}
						class="flex flex-1 items-center gap-2"
					>
						<input type="hidden" name="color" value={newTagColor} />
						<input
							id="settings-business-user-tag-new-name"
							name="name"
							type="text"
							bind:value={newTagName}
							class="flex-1 rounded-lg border border-gray-300 px-4 py-2 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
							placeholder={t('enter_tag_name')}
						/>

						<Button
							id="settings-business-user-tag-new-save"
							type="submit"
							disabled={isSubmitting}
							color="green"
							class="disabled:opacity-20"
						>
							<CheckOutline class="mr-2 h-4 w-4" />
							{t('add_tag')}
						</Button>
						<Button
							id="settings-business-user-tag-new-cancel"
							on:click={() => {
								isAddingTag = !isAddingTag;
								tagFormErrors = null;
							}}
							color="light"
						>
							{t('cancel')}
						</Button>
					</form>
				</div>
			{:else}
				<div class="flex justify-start">
					<Button
						id="settings-business-user-tag-add-button"
						on:click={() => {
							isAddingTag = !isAddingTag;
							tagFormErrors = null;
						}}
						color="blue"
					>
						<PlusOutline class="mr-2 inline-block h-4 w-4" />
						<span>{t('add_tag')}</span>
					</Button>
				</div>
			{/if}

			{#if tagFormErrors}
				<div id="settings-business-user-tag-new-error" class="mt-1 text-sm text-red-500">{tagFormErrors}</div>
			{/if}
		</div>
	</div>
</AccordionItem>
